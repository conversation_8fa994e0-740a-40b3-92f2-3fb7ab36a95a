# Task ID: 5
# Title: Implement JWT Access and Refresh Token Management
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Integrate `@nestjs/jwt` to issue JWT access tokens upon successful login and manage refresh tokens for persistent sessions.
# Details:
Configure `JwtModule` with secrets and expiration times for access/refresh tokens. Store refresh tokens securely (e.g., DB associated with user). Implement `/auth/refresh` endpoint. Implement `JwtStrategy` for route protection.

# Test Strategy:
Verify JWTs issued on login. Protected routes require JWT. Test token refresh. Test token expiration and invalid token handling.
