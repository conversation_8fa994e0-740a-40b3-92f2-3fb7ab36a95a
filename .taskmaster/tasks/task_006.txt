# Task ID: 6
# Title: Implement `<PERSON><PERSON><PERSON>` and `AiModel` Management
# Status: pending
# Dependencies: 1, 2
# Priority: high
# Description: Define `AiKey` (for LLM provider API keys) and `AiModel` (for configuring specific LLM models) entities. Implement CRUD operations, allowing System Admins to manage these.
# Details:
Prisma schema for `AiKey`: `id`, `provider String`, `apiKey String` (encrypted), `tenantId String?`. Prisma schema for `AiModel`: `id`, `name String`, `modelIdentifier String`, `aiKeyId String`, `tenantId String?`, `config Json?`. Implement `AiAdminModule` with services/controllers. Encrypt API keys at rest. Add 'Test Key' functionality.

# Test Strategy:
CRUD tests for `AiKey` and `AiModel`. Verify API key encryption/decryption. Test 'Test Key' functionality.
