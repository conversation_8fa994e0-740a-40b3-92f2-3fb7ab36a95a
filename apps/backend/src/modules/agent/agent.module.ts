import { Module } from '@nestjs/common';
import { AgentRunnerService } from './agent.service';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';

/**
 * Agent 模組
 * 
 * 負責管理和執行 LangChain 代理的核心模組
 * 提供代理運行、配置管理等功能
 */
@Module({
  imports: [
    PrismaModule, // 需要數據庫訪問
  ],
  providers: [
    AgentRunnerService,
  ],
  exports: [
    AgentRunnerService,
  ],
})
export class AgentModule {} 