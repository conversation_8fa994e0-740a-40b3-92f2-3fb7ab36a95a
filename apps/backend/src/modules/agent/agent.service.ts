import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';

/**
 * Agent 運行服務
 * 
 * 負責初始化和運行 LangChain 代理，支援多租戶架構
 * 提供代理配置管理、會話處理和結果返回等核心功能
 */
@Injectable()
export class AgentRunnerService {
  private readonly logger = new Logger(AgentRunnerService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 運行 AI 代理
   * 
   * @param userInput 使用者輸入內容
   * @param tenantId 租戶 ID
   * @param agentConfigId 可選的代理配置 ID，如未提供則使用預設配置
   * @returns 代理處理結果
   * 
   * @throws BadRequestException 當輸入參數無效時
   */
  async runAgent(
    userInput: string,
    tenantId: string,
    agentConfigId?: string,
  ): Promise<string> {
    this.logger.log(`開始運行代理 - 租戶: ${tenantId}, 配置: ${agentConfigId || '預設'}`);

    // 驗證輸入參數
    if (!userInput?.trim()) {
      throw new BadRequestException('使用者輸入不能為空');
    }

    if (!tenantId?.trim()) {
      throw new BadRequestException('租戶 ID 不能為空');
    }

    try {
      // TODO: 實現 LangChain 代理邏輯
      // 1. 根據 agentConfigId 載入代理配置（如果提供）
      // 2. 初始化 LangChain 代理實例
      // 3. 處理使用者輸入
      // 4. 執行代理推理
      // 5. 記錄會話歷史到資料庫
      // 6. 返回處理結果

      // 暫時返回示例回應
      const response = `代理回應（租戶: ${tenantId}）: 已收到您的輸入「${userInput}」，正在處理中...`;
      
      this.logger.log(`代理運行完成 - 租戶: ${tenantId}`);
      return response;

    } catch (error) {
      this.logger.error(`代理運行失敗 - 租戶: ${tenantId}`, error.stack);
      throw new BadRequestException('代理運行失敗，請稍後重試');
    }
  }

  /**
   * 驗證租戶存在性
   * 
   * @param tenantId 租戶 ID
   * @returns 租戶是否存在
   */
  private async validateTenant(tenantId: string): Promise<boolean> {
    try {
      const tenant = await this.prisma.tenants.findUnique({
        where: { id: tenantId },
        select: { id: true, status: true },
      });

      return tenant?.status === 'ACTIVE';
    } catch (error) {
      this.logger.error(`驗證租戶失敗: ${tenantId}`, error.stack);
      return false;
    }
  }

  /**
   * 載入代理配置
   * 
   * @param agentConfigId 代理配置 ID
   * @param tenantId 租戶 ID
   * @returns 代理配置資料
   */
  private async loadAgentConfig(
    agentConfigId: string,
    tenantId: string,
  ): Promise<any> {
    try {
      // TODO: 實現代理配置載入邏輯
      // 從資料庫載入指定的代理配置
      
      this.logger.log(`載入代理配置: ${agentConfigId} (租戶: ${tenantId})`);
      return null; // 暫時返回 null
    } catch (error) {
      this.logger.error(`載入代理配置失敗: ${agentConfigId}`, error.stack);
      throw new BadRequestException('載入代理配置失敗');
    }
  }

  /**
   * 記錄代理會話
   * 
   * @param tenantId 租戶 ID
   * @param userInput 使用者輸入
   * @param agentResponse 代理回應
   * @param agentConfigId 代理配置 ID
   */
  private async logAgentSession(
    tenantId: string,
    userInput: string,
    agentResponse: string,
    agentConfigId?: string,
  ): Promise<void> {
    try {
      // TODO: 實現會話記錄邏輯
      // 將會話資料儲存到資料庫，用於追蹤和分析
      
      this.logger.log(`記錄代理會話 - 租戶: ${tenantId}`);
    } catch (error) {
      this.logger.error(`記錄會話失敗 - 租戶: ${tenantId}`, error.stack);
      // 記錄失敗不應該影響主要流程，只記錄錯誤
    }
  }
} 